package com.smaile.health.service;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.Status;
import com.smaile.health.constants.UserStatus;
import com.smaile.health.domain.InsuranceCompany;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
import com.smaile.health.domain.UserOrganization;
import com.smaile.health.model.InsuranceCompanySearchDTO;
import com.smaile.health.repository.InsuranceCompanyRepository;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.UserOrganizationRepository;
import com.smaile.health.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration test for InsuranceCompanyService.
 * Tests the performance-optimized search functionality.
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class InsuranceCompanyServiceIntegrationTest {

    @Autowired
    private InsuranceCompanyService insuranceCompanyService;

    @Autowired
    private InsuranceCompanyRepository insuranceCompanyRepository;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserOrganizationRepository userOrganizationRepository;

    private Organization parentOrg;
    private InsuranceCompany testInsuranceCompany1;
    private InsuranceCompany testInsuranceCompany2;

    @BeforeEach
    void setUp() {
        // Create parent organization
        parentOrg = new Organization();
        parentOrg.setId(UUID.randomUUID());
        parentOrg.setName("SMAILE System");
        parentOrg.setType(OrganizationType.SUPER_SMAILE);
        parentOrg.setStatus(OrganizationStatus.ACTIVE);
        parentOrg.setDateCreated(OffsetDateTime.now());
        parentOrg.setCreatedBy("test");
        parentOrg = organizationRepository.save(parentOrg);

        // Create first insurance company
        testInsuranceCompany1 = new InsuranceCompany();
        testInsuranceCompany1.setId(UUID.randomUUID());
        testInsuranceCompany1.setName("ABC Insurance Co.");
        testInsuranceCompany1.setCode("ABC001");
        testInsuranceCompany1.setRegistrationNumber("REG123456");
        testInsuranceCompany1.setType(OrganizationType.IC);
        testInsuranceCompany1.setStatus(OrganizationStatus.ACTIVE);
        testInsuranceCompany1.setContactPhone("******-0123");
        testInsuranceCompany1.setContactEmail("<EMAIL>");
        testInsuranceCompany1.setAddress("123 Main St, New York, NY 10001");
        testInsuranceCompany1.setMarket("US");
        testInsuranceCompany1.setCountry("United States");
        testInsuranceCompany1.setParent(parentOrg);
        testInsuranceCompany1.setDateCreated(OffsetDateTime.now());
        testInsuranceCompany1.setCreatedBy("test");
        testInsuranceCompany1 = insuranceCompanyRepository.save(testInsuranceCompany1);

        // Create second insurance company
        testInsuranceCompany2 = new InsuranceCompany();
        testInsuranceCompany2.setId(UUID.randomUUID());
        testInsuranceCompany2.setName("XYZ Insurance Ltd.");
        testInsuranceCompany2.setCode("XYZ002");
        testInsuranceCompany2.setRegistrationNumber("REG789012");
        testInsuranceCompany2.setType(OrganizationType.IC);
        testInsuranceCompany2.setStatus(OrganizationStatus.ACTIVE);
        testInsuranceCompany2.setContactPhone("******-0456");
        testInsuranceCompany2.setContactEmail("<EMAIL>");
        testInsuranceCompany2.setAddress("456 Oak Ave, Los Angeles, CA 90001");
        testInsuranceCompany2.setMarket("US");
        testInsuranceCompany2.setCountry("United States");
        testInsuranceCompany2.setParent(parentOrg);
        testInsuranceCompany2.setDateCreated(OffsetDateTime.now());
        testInsuranceCompany2.setCreatedBy("test");
        testInsuranceCompany2 = insuranceCompanyRepository.save(testInsuranceCompany2);

        // Create test users and user-organization relationships
        createTestUsersAndRelationships();
    }

    private void createTestUsersAndRelationships() {
        // Create test users
        User testUser1 = new User();
        testUser1.setId(UUID.randomUUID());
        testUser1.setKeycloakId("keycloak-1");
        testUser1.setEmail("<EMAIL>");
        testUser1.setFullName("Admin User 1");
        testUser1.setPhone("******-1111");
        testUser1.setStatus(UserStatus.ACTIVE);
        testUser1.setDateCreated(OffsetDateTime.now());
        testUser1.setCreatedBy("test");
        testUser1 = userRepository.save(testUser1);

        User testUser2 = new User();
        testUser2.setId(UUID.randomUUID());
        testUser2.setKeycloakId("keycloak-2");
        testUser2.setEmail("<EMAIL>");
        testUser2.setFullName("Admin User 2");
        testUser2.setPhone("******-2222");
        testUser2.setStatus(UserStatus.ACTIVE);
        testUser2.setDateCreated(OffsetDateTime.now());
        testUser2.setCreatedBy("test");
        testUser2 = userRepository.save(testUser2);

        // Create user-organization relationships
        UserOrganization userOrg1 = new UserOrganization();
        userOrg1.setId(UUID.randomUUID());
        userOrg1.setUser(testUser1);
        userOrg1.setOrganization(testInsuranceCompany1);
        userOrg1.setStatus(Status.ACTIVE);
        userOrg1.setDateCreated(OffsetDateTime.now());
        userOrg1.setCreatedBy("test");
        userOrganizationRepository.save(userOrg1);

        UserOrganization userOrg2 = new UserOrganization();
        userOrg2.setId(UUID.randomUUID());
        userOrg2.setUser(testUser2);
        userOrg2.setOrganization(testInsuranceCompany1);
        userOrg2.setStatus(Status.ACTIVE);
        userOrg2.setDateCreated(OffsetDateTime.now());
        userOrg2.setCreatedBy("test");
        userOrganizationRepository.save(userOrg2);
    }

    @Test
    void search_ShouldReturnOptimizedResults() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<InsuranceCompanySearchDTO> result = insuranceCompanyService.search(
                null, null, null, pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(2);
        assertThat(result.getTotalElements()).isEqualTo(2);

        InsuranceCompanySearchDTO dto1 = result.getContent().stream()
                .filter(dto -> dto.getName().equals("ABC Insurance Co."))
                .findFirst()
                .orElse(null);

        assertThat(dto1).isNotNull();
        assertThat(dto1.getId()).isEqualTo(testInsuranceCompany1.getId());
        assertThat(dto1.getName()).isEqualTo("ABC Insurance Co.");
        assertThat(dto1.getCode()).isEqualTo("ABC001");
        assertThat(dto1.getRegistrationNumber()).isEqualTo("REG123456");
        assertThat(dto1.getStatus()).isEqualTo(OrganizationStatus.ACTIVE);
        assertThat(dto1.getContactPhone()).isEqualTo("******-0123");
        assertThat(dto1.getContactEmail()).isEqualTo("<EMAIL>");
        assertThat(dto1.getAddress()).isEqualTo("123 Main St, New York, NY 10001");
        assertThat(dto1.getMarket()).isEqualTo("US");
        assertThat(dto1.getCountry()).isEqualTo("United States");
        assertThat(dto1.getTotalAdmin()).isEqualTo(2); // Two users associated
        assertThat(dto1.getParent()).isNotNull();
        assertThat(dto1.getParent().getId()).isEqualTo(parentOrg.getId());
        assertThat(dto1.getParent().getName()).isEqualTo("SMAILE System");
    }

    @Test
    void search_WithMarketFilter_ShouldReturnFilteredResults() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<InsuranceCompanySearchDTO> result = insuranceCompanyService.search(
                "US", null, null, pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(2);
        assertThat(result.getContent()).allMatch(dto -> "US".equals(dto.getMarket()));
    }

    @Test
    void search_WithNameFilter_ShouldReturnFilteredResults() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<InsuranceCompanySearchDTO> result = insuranceCompanyService.search(
                null, null, "ABC", pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0).getName()).isEqualTo("ABC Insurance Co.");
    }

    @Test
    void search_WithStatusFilter_ShouldReturnFilteredResults() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<InsuranceCompanySearchDTO> result = insuranceCompanyService.search(
                null, "ACTIVE", null, pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(2);
        assertThat(result.getContent()).allMatch(dto -> OrganizationStatus.ACTIVE.equals(dto.getStatus()));
    }

    @Test
    void search_ShouldNotTriggerLazyLoadingIssues() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);

        // When - This should execute efficiently without N+1 queries
        long startTime = System.currentTimeMillis();
        Page<InsuranceCompanySearchDTO> result = insuranceCompanyService.search(
                null, null, null, pageable);
        long endTime = System.currentTimeMillis();

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(2);
        
        // Performance assertion - should complete quickly (under 1 second for test data)
        assertThat(endTime - startTime).isLessThan(1000);
        
        // Verify that all required data is populated without additional queries
        result.getContent().forEach(dto -> {
            assertThat(dto.getId()).isNotNull();
            assertThat(dto.getName()).isNotNull();
            assertThat(dto.getTotalAdmin()).isNotNull();
            assertThat(dto.getParent()).isNotNull();
            assertThat(dto.getParent().getId()).isNotNull();
            assertThat(dto.getParent().getName()).isNotNull();
        });
    }
}
