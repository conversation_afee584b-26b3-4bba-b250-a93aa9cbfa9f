package com.smaile.health.repository.custom;

import com.smaile.health.model.InsuranceCompanySearchDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

/**
 * Custom repository interface for InsuranceCompany entity.
 * Provides methods for efficient data retrieval using projections and custom queries
 * to avoid performance issues with lazy loading.
 */
public interface InsuranceCompanyCustomRepository {

    /**
     * Search for insurance companies with efficient data fetching.
     * Uses projections to avoid N+1 queries and lazy loading issues.
     *
     * @param market the market filter (optional)
     * @param status the status filter (optional)
     * @param name the name filter for partial matching (optional)
     * @param parentOrgId the parent organization ID for filtering (optional)
     * @param pageable pagination parameters
     * @return Page of InsuranceCompanySearchDTO with efficient data loading
     */
    Page<InsuranceCompanySearchDTO> searchWithProjection(
            String market,
            String status,
            String name,
            UUID parentOrgId,
            Pageable pageable
    );

    /**
     * Get a single insurance company by ID with efficient data fetching.
     * Uses projections to avoid lazy loading issues.
     *
     * @param id the insurance company ID
     * @return InsuranceCompanySearchDTO or null if not found
     */
    InsuranceCompanySearchDTO findByIdWithProjection(UUID id);
}
