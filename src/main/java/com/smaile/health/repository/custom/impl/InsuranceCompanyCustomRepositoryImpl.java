package com.smaile.health.repository.custom.impl;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.Status;
import com.smaile.health.domain.InsuranceCompany;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.UserOrganization;
import com.smaile.health.model.InsuranceCompanySearchDTO;
import com.smaile.health.model.ParentOrganizationDTO;
import com.smaile.health.repository.custom.InsuranceCompanyCustomRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Custom repository implementation for InsuranceCompany entity.
 * Uses Criteria API for efficient data retrieval with projections.
 */
@Repository
@Slf4j
public class InsuranceCompanyCustomRepositoryImpl implements InsuranceCompanyCustomRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<InsuranceCompanySearchDTO> searchWithProjection(
            String market,
            String status,
            String name,
            UUID parentOrgId,
            Pageable pageable) {

        log.debug("Executing search with projection - Market: {}, Status: {}, Name: {}, ParentOrgId: {}",
                market, status, name, parentOrgId);

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();

        // Main query for data
        CriteriaQuery<InsuranceCompanySearchDTO> query = cb.createQuery(InsuranceCompanySearchDTO.class);
        Root<InsuranceCompany> root = query.from(InsuranceCompany.class);
        Join<InsuranceCompany, Organization> parentJoin = root.join("parent", JoinType.LEFT);

        // Subquery to count active users
        Subquery<Long> userCountSubquery = query.subquery(Long.class);
        Root<UserOrganization> userOrgRoot = userCountSubquery.from(UserOrganization.class);
        userCountSubquery.select(cb.count(userOrgRoot))
                .where(
                        cb.equal(userOrgRoot.get("organization").get("id"), root.get("id")),
                        cb.equal(userOrgRoot.get("status"), Status.ACTIVE)
                );

        // Select with projection
        query.select(cb.construct(
                InsuranceCompanySearchDTO.class,
                root.get("id"),
                root.get("name"),
                root.get("code"),
                root.get("registrationNumber"),
                root.get("status"),
                root.get("contactPhone"),
                root.get("contactEmail"),
                root.get("address"),
                root.get("market"),
                root.get("country"),
                cb.coalesce(userCountSubquery, 0L).as(Integer.class),
                cb.construct(
                        ParentOrganizationDTO.class,
                        parentJoin.get("id"),
                        parentJoin.get("name")
                ),
                root.get("dateCreated"),
                root.get("createdBy"),
                root.get("lastUpdated"),
                root.get("updatedBy")
        ));

        // Apply filters
        List<Predicate> predicates = buildPredicates(cb, root, market, status, name, parentOrgId);
        query.where(predicates.toArray(new Predicate[0]));

        // Apply sorting
        if (pageable.getSort().isSorted()) {
            pageable.getSort().forEach(order -> {
                if (order.isAscending()) {
                    query.orderBy(cb.asc(root.get(order.getProperty())));
                } else {
                    query.orderBy(cb.desc(root.get(order.getProperty())));
                }
            });
        } else {
            query.orderBy(cb.desc(root.get("dateCreated")));
        }

        // Execute main query
        TypedQuery<InsuranceCompanySearchDTO> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<InsuranceCompanySearchDTO> results = typedQuery.getResultList();

        // Count query
        long total = getSearchCount(market, status, name, parentOrgId);

        log.debug("Search completed - Found {} results out of {} total", results.size(), total);
        return new PageImpl<>(results, pageable, total);
    }

    @Override
    public InsuranceCompanySearchDTO findByIdWithProjection(UUID id) {
        log.debug("Finding insurance company by ID with projection: {}", id);

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<InsuranceCompanySearchDTO> query = cb.createQuery(InsuranceCompanySearchDTO.class);
        Root<InsuranceCompany> root = query.from(InsuranceCompany.class);
        Join<InsuranceCompany, Organization> parentJoin = root.join("parent", JoinType.LEFT);

        // Subquery to count active users
        Subquery<Long> userCountSubquery = query.subquery(Long.class);
        Root<UserOrganization> userOrgRoot = userCountSubquery.from(UserOrganization.class);
        userCountSubquery.select(cb.count(userOrgRoot))
                .where(
                        cb.equal(userOrgRoot.get("organization").get("id"), root.get("id")),
                        cb.equal(userOrgRoot.get("status"), Status.ACTIVE)
                );

        // Select with projection
        query.select(cb.construct(
                InsuranceCompanySearchDTO.class,
                root.get("id"),
                root.get("name"),
                root.get("code"),
                root.get("registrationNumber"),
                root.get("status"),
                root.get("contactPhone"),
                root.get("contactEmail"),
                root.get("address"),
                root.get("market"),
                root.get("country"),
                cb.coalesce(userCountSubquery, 0L).as(Integer.class),
                cb.construct(
                        ParentOrganizationDTO.class,
                        parentJoin.get("id"),
                        parentJoin.get("name")
                ),
                root.get("dateCreated"),
                root.get("createdBy"),
                root.get("lastUpdated"),
                root.get("updatedBy")
        ));

        query.where(
                cb.equal(root.get("id"), id),
                cb.notEqual(root.get("status"), OrganizationStatus.ARCHIVED)
        );

        List<InsuranceCompanySearchDTO> results = entityManager.createQuery(query).getResultList();
        return results.isEmpty() ? null : results.get(0);
    }

    private List<Predicate> buildPredicates(CriteriaBuilder cb, Root<InsuranceCompany> root,
                                            String market, String status, String name, UUID parentOrgId) {
        List<Predicate> predicates = new ArrayList<>();

        // Ensure we only work with InsuranceCompany entities (type = 'IC')
        predicates.add(cb.equal(root.get("type"), OrganizationType.IC));

        // Market filter
        if (StringUtils.hasText(market)) {
            predicates.add(cb.equal(root.get("market"), market));
        }

        // Status filter
        if (StringUtils.hasText(status)) {
            try {
                OrganizationStatus statusEnum = OrganizationStatus.fromString(status);
                predicates.add(cb.equal(root.get("status"), statusEnum));
            } catch (IllegalArgumentException e) {
                predicates.add(cb.equal(root.get("status"), OrganizationStatus.ACTIVE));
            }
        } else {
            predicates.add(cb.notEqual(root.get("status"), OrganizationStatus.ARCHIVED));
        }

        // Name filter (case-insensitive partial match)
        if (StringUtils.hasText(name)) {
            predicates.add(cb.like(
                    cb.lower(cb.coalesce(root.get("name"), "")),
                    "%" + name.toLowerCase() + "%"
            ));
        }

        // Parent organization filter
        if (parentOrgId != null) {
            predicates.add(cb.equal(root.get("parent").get("id"), parentOrgId));
        }

        return predicates;
    }

    private long getSearchCount(String market, String status, String name, UUID parentOrgId) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<InsuranceCompany> root = countQuery.from(InsuranceCompany.class);

        countQuery.select(cb.count(root));

        List<Predicate> predicates = buildPredicates(cb, root, market, status, name, parentOrgId);
        countQuery.where(predicates.toArray(new Predicate[0]));

        return entityManager.createQuery(countQuery).getSingleResult();
    }
}
