package com.smaile.health.repository.custom.impl;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.Status;
import com.smaile.health.domain.InsuranceCompany;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.UserOrganization;
import com.smaile.health.model.InsuranceCompanySearchDTO;
import com.smaile.health.model.ParentOrganizationDTO;
import com.smaile.health.repository.custom.InsuranceCompanyCustomRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Custom repository implementation for InsuranceCompany entity.
 * Uses Criteria API for efficient data retrieval with projections.
 */
@Repository
@Slf4j
public class InsuranceCompanyCustomRepositoryImpl implements InsuranceCompanyCustomRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<InsuranceCompanySearchDTO> searchWithProjection(
            String market,
            String status,
            String name,
            UUID parentOrgId,
            Pageable pageable) {

        log.debug("Executing search with projection - Market: {}, Status: {}, Name: {}, ParentOrgId: {}",
                market, status, name, parentOrgId);

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();

        // Main query for data - use Object[] to avoid constructor issues
        CriteriaQuery<Object[]> query = cb.createQuery(Object[].class);
        Root<InsuranceCompany> root = query.from(InsuranceCompany.class);
        Join<InsuranceCompany, Organization> parentJoin = root.join("parent", JoinType.LEFT);

        // Select fields as Object array
        query.multiselect(
                root.get("id"),
                root.get("name"),
                root.get("code"),
                root.get("registrationNumber"),
                root.get("status"),
                root.get("contactPhone"),
                root.get("contactEmail"),
                root.get("address"),
                root.get("market"),
                root.get("country"),
                parentJoin.get("id"),
                parentJoin.get("name"),
                root.get("dateCreated"),
                root.get("createdBy"),
                root.get("lastUpdated"),
                root.get("updatedBy")
        );

        // Apply filters
        List<Predicate> predicates = buildPredicates(cb, root, market, status, name, parentOrgId);
        query.where(predicates.toArray(new Predicate[0]));

        // Apply sorting
        if (pageable.getSort().isSorted()) {
            pageable.getSort().forEach(order -> {
                if (order.isAscending()) {
                    query.orderBy(cb.asc(root.get(order.getProperty())));
                } else {
                    query.orderBy(cb.desc(root.get(order.getProperty())));
                }
            });
        } else {
            query.orderBy(cb.desc(root.get("dateCreated")));
        }

        // Execute main query
        TypedQuery<Object[]> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<Object[]> rawResults = typedQuery.getResultList();

        // Convert to DTOs and get user counts
        List<InsuranceCompanySearchDTO> results = rawResults.stream()
                .map(this::mapToInsuranceCompanySearchDTO)
                .collect(Collectors.toList());

        // Count query
        long total = getSearchCount(market, status, name, parentOrgId);

        log.debug("Search completed - Found {} results out of {} total", results.size(), total);
        return new PageImpl<>(results, pageable, total);
    }

    @Override
    public InsuranceCompanySearchDTO findByIdWithProjection(UUID id) {
        log.debug("Finding insurance company by ID with projection: {}", id);

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Object[]> query = cb.createQuery(Object[].class);
        Root<InsuranceCompany> root = query.from(InsuranceCompany.class);
        Join<InsuranceCompany, Organization> parentJoin = root.join("parent", JoinType.LEFT);

        // Select fields as Object array
        query.multiselect(
                root.get("id"),
                root.get("name"),
                root.get("code"),
                root.get("registrationNumber"),
                root.get("status"),
                root.get("contactPhone"),
                root.get("contactEmail"),
                root.get("address"),
                root.get("market"),
                root.get("country"),
                parentJoin.get("id"),
                parentJoin.get("name"),
                root.get("dateCreated"),
                root.get("createdBy"),
                root.get("lastUpdated"),
                root.get("updatedBy")
        );

        query.where(
                cb.equal(root.get("id"), id),
                cb.notEqual(root.get("status"), OrganizationStatus.ARCHIVED)
        );

        List<Object[]> results = entityManager.createQuery(query).getResultList();
        return results.isEmpty() ? null : mapToInsuranceCompanySearchDTO(results.get(0));
    }

    private List<Predicate> buildPredicates(CriteriaBuilder cb, Root<InsuranceCompany> root,
                                            String market, String status, String name, UUID parentOrgId) {
        List<Predicate> predicates = new ArrayList<>();

        // Ensure we only work with InsuranceCompany entities (type = 'IC')
        predicates.add(cb.equal(root.get("type"), OrganizationType.IC));

        // Market filter
        if (StringUtils.hasText(market)) {
            predicates.add(cb.equal(root.get("market"), market));
        }

        // Status filter
        if (StringUtils.hasText(status)) {
            try {
                OrganizationStatus statusEnum = OrganizationStatus.fromString(status);
                predicates.add(cb.equal(root.get("status"), statusEnum));
            } catch (IllegalArgumentException e) {
                predicates.add(cb.equal(root.get("status"), OrganizationStatus.ACTIVE));
            }
        } else {
            predicates.add(cb.notEqual(root.get("status"), OrganizationStatus.ARCHIVED));
        }

        // Name filter (case-insensitive partial match)
        if (StringUtils.hasText(name)) {
            predicates.add(cb.like(
                    cb.lower(cb.coalesce(root.get("name"), "")),
                    "%" + name.toLowerCase() + "%"
            ));
        }

        // Parent organization filter
        if (parentOrgId != null) {
            predicates.add(cb.equal(root.get("parent").get("id"), parentOrgId));
        }

        return predicates;
    }

    private long getSearchCount(String market, String status, String name, UUID parentOrgId) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<InsuranceCompany> root = countQuery.from(InsuranceCompany.class);

        countQuery.select(cb.count(root));

        List<Predicate> predicates = buildPredicates(cb, root, market, status, name, parentOrgId);
        countQuery.where(predicates.toArray(new Predicate[0]));

        return entityManager.createQuery(countQuery).getSingleResult();
    }

    private InsuranceCompanySearchDTO mapToInsuranceCompanySearchDTO(Object[] row) {
        UUID id = (UUID) row[0];
        String name = (String) row[1];
        String code = (String) row[2];
        String registrationNumber = (String) row[3];
        OrganizationStatus status = (OrganizationStatus) row[4];
        String contactPhone = (String) row[5];
        String contactEmail = (String) row[6];
        String address = (String) row[7];
        String market = (String) row[8];
        String country = (String) row[9];
        UUID parentId = (UUID) row[10];
        String parentName = (String) row[11];
        OffsetDateTime dateCreated = (OffsetDateTime) row[12];
        String createdBy = (String) row[13];
        OffsetDateTime lastUpdated = (OffsetDateTime) row[14];
        String updatedBy = (String) row[15];

        // Get user count for this insurance company
        Integer totalAdmin = getUserCount(id);

        // Create parent DTO
        ParentOrganizationDTO parent = null;
        if (parentId != null) {
            parent = new ParentOrganizationDTO(parentId, parentName);
        }

        return InsuranceCompanySearchDTO.builder()
                .id(id)
                .name(name)
                .code(code)
                .registrationNumber(registrationNumber)
                .status(status)
                .contactPhone(contactPhone)
                .contactEmail(contactEmail)
                .address(address)
                .market(market)
                .country(country)
                .totalAdmin(totalAdmin)
                .parent(parent)
                .dateCreated(dateCreated)
                .createdBy(createdBy)
                .lastUpdated(lastUpdated)
                .updatedBy(updatedBy)
                .build();
    }

    private Integer getUserCount(UUID organizationId) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<UserOrganization> root = countQuery.from(UserOrganization.class);

        countQuery.select(cb.count(root))
                .where(
                        cb.equal(root.get("organization").get("id"), organizationId),
                        cb.equal(root.get("status"), Status.ACTIVE)
                );

        Long count = entityManager.createQuery(countQuery).getSingleResult();
        return count != null ? count.intValue() : 0;
    }
}
