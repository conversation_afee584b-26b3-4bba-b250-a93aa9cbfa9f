package com.smaile.health.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.exception.SmaileAuthenticationException;
import com.smaile.health.model.response.ErrorResponse;
import com.smaile.health.security.authentication.SmaileAuthenticationFilter;
import com.smaile.health.security.authentication.SmaileAuthenticationProvider;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.io.IOException;

import static com.smaile.health.config.GlobalExceptionHandler.ACCESS_DENIED_CODE;
import static com.smaile.health.config.GlobalExceptionHandler.ACCESS_DENIED_MESSAGE;
import static com.smaile.health.config.GlobalExceptionHandler.BAD_CREDENTIALS_CODE;
import static com.smaile.health.config.GlobalExceptionHandler.BAD_CREDENTIALS_MESSAGE;
import static com.smaile.health.config.GlobalExceptionHandler.SMAILE_AUTHENTICATION_ERROR_CODE;
import static com.smaile.health.config.GlobalExceptionHandler.SMAILE_AUTHENTICATION_ERROR_MESSAGE;

/**
 * Web Security Configuration for SMAILE Health platform.
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/26
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true)
@Profile("!test")
@RequiredArgsConstructor
public class WebSecurityConfig {

    public static final String[] WHITELIST = {
            "/v3/api-docs/**",
            "/swagger-ui/**",
            "/utilities/**",
            "/register/**",
            "/actuator/**"
    };
    private final SmaileAuthenticationProvider authenticationProvider;
    private final ObjectMapper objectMapper;
    private final CorsProperties corsProperties;
    @Value("${server.servlet.context-path}")
    private String contextPath;

    /**
     * Configures the authentication manager with custom authentication provider.
     *
     * @return AuthenticationManager configured with SmaileAuthenticationProvider
     */
    @Bean
    public AuthenticationManager authenticationManager() {
        return new ProviderManager(authenticationProvider);
    }

    /**
     * Configures the main security filter chain with support for both header-based
     *
     * @param http HttpSecurity configuration
     * @return SecurityFilterChain configured for SMAILE Health
     * @throws Exception if configuration fails
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        SmaileAuthenticationFilter authenticationFilter = new SmaileAuthenticationFilter(authenticationManager(),
                contextPath);

        http.cors(cors -> cors.configurationSource(corsConfigurationSource()))
                .csrf(AbstractHttpConfigurer::disable)
                .exceptionHandling(exceptionHandling -> exceptionHandling
                        .authenticationEntryPoint(authenticationEntryPoint())
                        .accessDeniedHandler(accessDeniedHandler())
                )
                .addFilterBefore(authenticationFilter, UsernamePasswordAuthenticationFilter.class)
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers(WHITELIST).permitAll()
                        .anyRequest().authenticated()
                )
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                );

        return http.build();
    }

    @Bean
    public AuthenticationEntryPoint authenticationEntryPoint() {
        return (HttpServletRequest request, HttpServletResponse response,
                AuthenticationException authException) -> {
            if (authException instanceof DisabledException || authException instanceof LockedException) {
                writeErrorResponse(response, HttpStatus.UNAUTHORIZED,
                        GlobalExceptionHandler.ACCOUNT_DISABLED_OR_LOCKED_CODE,
                        GlobalExceptionHandler.ACCOUNT_DISABLED_OR_LOCKED_MESSAGE,
                        "User account is disabled or locked",
                        request.getRequestURI());
            } else if (authException instanceof InsufficientAuthenticationException) {
                writeErrorResponse(response, HttpStatus.UNAUTHORIZED,
                        SMAILE_AUTHENTICATION_ERROR_CODE,
                        SMAILE_AUTHENTICATION_ERROR_MESSAGE,
                        "Header X-FORWARDED-SMAILE-USER is required",
                        request.getRequestURI());
            } else if (authException instanceof SmaileAuthenticationException) {
                writeErrorResponse(response, HttpStatus.UNAUTHORIZED,
                        SMAILE_AUTHENTICATION_ERROR_CODE,
                        SMAILE_AUTHENTICATION_ERROR_MESSAGE,
                        "Authentication is required to access this resource",
                        request.getRequestURI());
            } else {
                writeErrorResponse(response, HttpStatus.UNAUTHORIZED,
                        SMAILE_AUTHENTICATION_ERROR_CODE,
                        SMAILE_AUTHENTICATION_ERROR_MESSAGE,
                        "Authentication failed",
                        request.getRequestURI());
            }
        };
    }

    @Bean
    public AccessDeniedHandler accessDeniedHandler() {
        return (HttpServletRequest request, HttpServletResponse response,
                AccessDeniedException accessDeniedException) ->
                writeErrorResponse(response, HttpStatus.FORBIDDEN,
                        ACCESS_DENIED_CODE,
                        ACCESS_DENIED_MESSAGE,
                        "Insufficient permissions for the requested operation",
                        request.getRequestURI());
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();

        configuration.setAllowedOrigins(corsProperties.getAllowedOrigins());
        configuration.setAllowedMethods(corsProperties.getAllowedMethods());
        configuration.setAllowedHeaders(corsProperties.getAllowedHeaders());
        configuration.setAllowCredentials(corsProperties.isAllowCredentials());
        configuration.setMaxAge(corsProperties.getMaxAge());

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    /**
     * Helper method to write error responses in a consistent format.
     */
    private void writeErrorResponse(HttpServletResponse response, HttpStatus status,
                                    String errorCode, String message, String detail, String path) throws IOException {
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(status.value());
        ErrorResponse errorResponse = ErrorResponse.of(status.value(), errorCode, message, detail, path);
        response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
    }

}
