package com.smaile.health.config;

import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.ReferencedException;
import com.smaile.health.exception.SmaileAuthenticationException;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.SmaileValidationException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.model.response.ErrorResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Global exception handler for the SMAILE Health application.
 * Provides consistent error responses across all REST endpoints.
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/27
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    public static final String VALIDATION_FAILED_CODE = "VALIDATION_FAILED";
    public static final String VALIDATION_FAILED_MESSAGE = "Validation Failed";
    public static final String CONSTRAINT_VIOLATION_CODE = "CONSTRAINT_VIOLATION";
    public static final String CONSTRAINT_VIOLATION_MESSAGE = "Constraint Violation";
    public static final String ACCOUNT_DISABLED_OR_LOCKED_CODE = "ACCOUNT_DISABLED_OR_LOCKED";
    public static final String ACCOUNT_DISABLED_OR_LOCKED_MESSAGE = "Account Disabled/Locked";
    public static final String BAD_CREDENTIALS_CODE = "BAD_CREDENTIALS";
    public static final String BAD_CREDENTIALS_MESSAGE = "Header X-FORWARDED-SMAILE-USER is required";
    public static final String SMAILE_AUTHENTICATION_ERROR_CODE = "SMAILE_AUTHENTICATION_ERROR";
    public static final String INTERNAL_SERVER_ERROR_CODE = "INTERNAL_SERVER_ERROR";
    public static final String INTERNAL_SERVER_ERROR_MESSAGE = "Internal Server Error";
    public static final String ACCESS_DENIED_CODE = "ACCESS_DENIED";
    public static final String ACCESS_DENIED_MESSAGE = "Access Denied";
    public static final String SMAILE_AUTHENTICATION_ERROR_MESSAGE = "SMAILE Authentication Error";

    /**
     * Handles SmaileRuntimeException - general runtime errors in the application.
     */
    @ExceptionHandler(SmaileRuntimeException.class)
    public ResponseEntity<ErrorResponse> handleSmaileRuntimeException(
            SmaileRuntimeException exception,
            HttpServletRequest request) {

        log.warn("SmaileRuntimeException occurred: {}", exception.getMessage(), exception);

        ErrorResponse errorResponse = ErrorResponse.of(
                HttpStatus.BAD_REQUEST.value(),
                "RUNTIME_ERROR",
                "Runtime Error",
                exception.getMessage(),
                request.getRequestURI()
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Handles NotFoundException - when requested resources cannot be found.
     */
    @ExceptionHandler(NotFoundException.class)
    public ResponseEntity<ErrorResponse> handleNotFoundException(
            NotFoundException exception,
            HttpServletRequest request) {

        log.info("NotFoundException occurred: {}", exception.getMessage());

        ErrorResponse errorResponse = ErrorResponse.of(
                HttpStatus.NOT_FOUND.value(),
                "RESOURCE_NOT_FOUND",
                "Resource Not Found",
                exception.getMessage(),
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }

    /**
     * Handles ReferencedException - when attempting to delete entities that are referenced by others.
     */
    @ExceptionHandler(ReferencedException.class)
    public ResponseEntity<ErrorResponse> handleReferencedException(
            ReferencedException exception,
            HttpServletRequest request) {

        log.warn("ReferencedException occurred: {}", exception.getMessage(), exception);

        ErrorResponse errorResponse = ErrorResponse.of(
                HttpStatus.CONFLICT.value(),
                "REFERENCED_ENTITY",
                "Referenced Entity",
                "Cannot delete entity because it is referenced by other entities",
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse);
    }

    /**
     * Handles ValidationException - general validation errors in business logic.
     */
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ErrorResponse> handleValidationException(
            ValidationException exception,
            HttpServletRequest request) {

        log.warn("ValidationException occurred: {}", exception.getMessage(), exception);

        ErrorResponse errorResponse = ErrorResponse.of(
                HttpStatus.BAD_REQUEST.value(),
                VALIDATION_FAILED_CODE,
                VALIDATION_FAILED_MESSAGE,
                exception.getMessage(),
                request.getRequestURI()
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Handles ConstraintViolationException - constraint violations.
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ErrorResponse> handleConstraintViolationException(
            ConstraintViolationException exception,
            HttpServletRequest request) {

        log.warn("ConstraintViolationException occurred: {} violations", exception.getConstraintViolations().size(),
                exception);

        Map<String, Object> violations = new HashMap<>();
        violations.put("violations", exception.getConstraintViolations().stream()
                .collect(Collectors.toMap(
                        violation -> violation.getPropertyPath().toString(),
                        ConstraintViolation::getMessage,
                        (existing, replacement) -> existing // Handle duplicate keys
                )));

        ErrorResponse errorResponse = ErrorResponse.of(
                HttpStatus.BAD_REQUEST.value(),
                CONSTRAINT_VIOLATION_CODE,
                CONSTRAINT_VIOLATION_MESSAGE,
                "One or more constraints were violated",
                request.getRequestURI(),
                violations
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Handles MethodArgumentNotValidException - Spring MVC validation failures.
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleMethodArgumentNotValidException(
            MethodArgumentNotValidException exception,
            HttpServletRequest request) {

        log.warn("MethodArgumentNotValidException occurred: {} field errors", exception.getFieldErrors().size(),
                exception);

        Map<String, Object> fieldErrors = new HashMap<>();
        fieldErrors.put("fieldErrors", exception.getFieldErrors().stream()
                .collect(Collectors.toMap(
                        FieldError::getField,
                        error -> error.getDefaultMessage() != null ? error.getDefaultMessage() : "Invalid value",
                        (existing, replacement) -> existing // Handle duplicate keys
                )));

        ErrorResponse errorResponse = ErrorResponse.of(
                HttpStatus.BAD_REQUEST.value(),
                VALIDATION_FAILED_CODE,
                VALIDATION_FAILED_MESSAGE,
                "Request validation failed",
                request.getRequestURI(),
                fieldErrors
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Handles SmaileValidationException - custom validation errors with structured error maps.
     */
    @ExceptionHandler(SmaileValidationException.class)
    public ResponseEntity<ErrorResponse> handleSmaileValidationException(
            SmaileValidationException exception,
            HttpServletRequest request) {

        log.warn("SmaileValidationException occurred: {} errors", exception.getErrorMap().size(), exception);

        Map<String, Object> errors = new HashMap<>();
        errors.put("errors", exception.getErrorMap());

        ErrorResponse errorResponse = ErrorResponse.of(
                HttpStatus.BAD_REQUEST.value(),
                VALIDATION_FAILED_CODE,
                VALIDATION_FAILED_MESSAGE,
                "Custom validation failed",
                request.getRequestURI(),
                errors
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Handles DisabledException - user account is disabled.
     */
    @ExceptionHandler({ DisabledException.class, LockedException.class })
    public ResponseEntity<ErrorResponse> handleDisabledException(
            DisabledException exception,
            HttpServletRequest request) {

        log.warn("DisabledException occurred: {}", exception.getMessage());

        ErrorResponse errorResponse = ErrorResponse.of(
                HttpStatus.UNAUTHORIZED.value(),
                ACCOUNT_DISABLED_OR_LOCKED_CODE,
                ACCOUNT_DISABLED_OR_LOCKED_MESSAGE,
                "User account is disabled",
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
    }

    /**
     * Handles BadCredentialsException - Header X-FORWARDED-SMAILE-USER is required.
     */
    @ExceptionHandler({ BadCredentialsException.class })
    public ResponseEntity<ErrorResponse> handleBadCredentialsException(
            BadCredentialsException exception,
            HttpServletRequest request) {

        log.warn("BadCredentialsException occurred: {}", exception.getMessage());

        ErrorResponse errorResponse = ErrorResponse.of(
                HttpStatus.UNAUTHORIZED.value(),
                BAD_CREDENTIALS_CODE,
                BAD_CREDENTIALS_MESSAGE,
                exception.getMessage(),
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
    }

    /**
     * Handles SmaileAuthenticationException - custom authentication errors specific to SMAILE platform.
     */
    @ExceptionHandler({ SmaileAuthenticationException.class, AuthenticationException.class })
    public ResponseEntity<ErrorResponse> handleSmaileAuthenticationException(
            SmaileAuthenticationException exception,
            HttpServletRequest request) {

        log.warn("SmaileAuthenticationException occurred: {}", exception.getMessage(), exception);

        ErrorResponse errorResponse = ErrorResponse.of(
                HttpStatus.UNAUTHORIZED.value(),
                SMAILE_AUTHENTICATION_ERROR_CODE,
                SMAILE_AUTHENTICATION_ERROR_MESSAGE,
                exception.getMessage(),
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
    }

    /**
     * Handles AccessDeniedException - user lacks sufficient permissions for the requested operation.
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ErrorResponse> handleAccessDeniedException(
            AccessDeniedException exception,
            HttpServletRequest request) {

        log.warn("AccessDeniedException occurred: {}", exception.getMessage());

        ErrorResponse errorResponse = ErrorResponse.of(
                HttpStatus.FORBIDDEN.value(),
                ACCESS_DENIED_CODE,
                ACCESS_DENIED_MESSAGE,
                "Insufficient permissions for the requested operation",
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorResponse);
    }

    /**
     * Handles any unexpected exceptions that aren't caught by specific handlers.
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(
            Exception exception,
            HttpServletRequest request) {

        log.error("Unexpected exception occurred: {}", exception.getMessage(), exception);

        ErrorResponse errorResponse = ErrorResponse.of(
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                INTERNAL_SERVER_ERROR_CODE,
                INTERNAL_SERVER_ERROR_MESSAGE,
                "An unexpected error occurred. Please contact support",
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

}
