package com.smaile.health.security.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * Represents a structured security permission with resource, action, and scope.
 * This is an immutable value object that encapsulates permission logic.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmaileSecurityPermission {

    private Resource resource;
    private Scope scope;
    private Action action;

    /**
     * Create a permission from string format: resource:scope:action
     */
    public static SmaileSecurityPermission fromString(String permissionString) {
        if (permissionString == null || permissionString.trim().isEmpty()) {
            throw new IllegalArgumentException("Permission string cannot be null or empty");
        }

        String[] parts = permissionString.split(":");
        if (parts.length != 3) {
            throw new IllegalArgumentException(
                    "Invalid permission format. Expected: resource:action:scope");
        }

        Resource resource = Resource.fromCode(parts[0]);
        Scope scope = Scope.fromCode(parts[1]);
        Action action = Action.fromCode(parts[2]);

        return SmaileSecurityPermission.builder()
                .resource(resource)
                .scope(scope)
                .action(action)
                .build();
    }

    /**
     * Create a wildcard permission for a resource
     */
    public static SmaileSecurityPermission wildcard(Resource resource, Scope scope) {
        return SmaileSecurityPermission.builder()
                .resource(resource)
                .scope(scope)
                .action(Action.MANAGE)
                .build();
    }

    /**
     * Convert permission to string format
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(resource.getCode())
                .append(":")
                .append(action.getCode())
                .append(":")
                .append(scope.getCode());

        return sb.toString();
    }

    /**
     * Check if this permission implies another permission
     */
    public boolean implies(SmaileSecurityPermission other) {
        if (other == null) {
            return false;
        }

        // Resource must match exactly or be a wildcard
        if (!this.resource.equals(other.resource)) {
            return false;
        }

        // Action must match exactly or this must be MANAGE (which implies all actions)
        if (!this.action.equals(other.action) && this.action != Action.MANAGE) {
            return false;
        }

        // Scope must include the other scope
        return this.scope.includes(other.scope);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SmaileSecurityPermission that = (SmaileSecurityPermission) o;
        return resource == that.resource &&
                scope == that.scope &&
                action == that.action;
    }

    @Override
    public int hashCode() {
        return Objects.hash(resource, scope, action);
    }

}
