package com.smaile.health.domain.projection;

import com.smaile.health.constants.OrganizationStatus;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * Projection interface for efficient InsuranceCompany data retrieval.
 * Used to fetch only necessary fields and avoid lazy loading issues.
 */
public interface InsuranceCompanySearchProjection {

    UUID getId();

    String getName();

    String getCode();

    String getRegistrationNumber();

    OrganizationStatus getStatus();

    String getContactPhone();

    String getContactEmail();

    String getAddress();

    String getMarket();

    String getCountry();

    OffsetDateTime getDateCreated();

    String getCreatedBy();

    OffsetDateTime getLastUpdated();

    String getUpdatedBy();

    // Parent organization fields
    UUID getParentId();

    String getParentName();

    // Calculated field for total admin count
    Long getTotalAdmin();
}
