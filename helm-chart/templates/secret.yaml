# The secret of this service can be defined here
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "chart.name" . }}-db-secret
type: Opaque

stringData:
  password: 3WFQBX]fqX

---

# The secret of this service can be defined here
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "chart.name" . }}-iam-secret
type: Opaque

stringData:
  client-secret: B7ca4tWTJdSK4g1L8yq0mi7xmISNydiB

---

apiVersion: v1
kind: Secret
metadata:
  name: {{ include "chart.name" . }}-email-secret
type: Opaque

stringData:
  access-key: 67NJJbYI1z8WMQaYsleqfTPmTqe1ZyPzfEjOfwd4CBaVY3QTwW6sJQQJ99BHACULyCpQD44pAAAAAZCSwZiI